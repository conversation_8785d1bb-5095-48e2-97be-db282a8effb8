/**
 * Advanced Location Caching Service
 * Provides intelligent caching with accuracy thresholds and background refresh
 */

import { LocationCoordinates } from './geolocationService';

export interface CachedLocationEntry {
  id: string;
  coordinates: LocationCoordinates;
  displayName: string;
  timestamp: number;
  accuracy: number;
  source: string;
  usageCount: number;
  lastUsed: number;
  isBackground?: boolean;
}

export interface LocationCacheConfig {
  maxEntries: number;
  defaultTTL: number; // Time to live in milliseconds
  accuracyThreshold: number; // Minimum accuracy in meters
  backgroundRefreshInterval: number;
  enablePersistence: boolean;
}

class LocationCache {
  private static instance: LocationCache;
  private cache = new Map<string, CachedLocationEntry>();
  private config: LocationCacheConfig;
  private backgroundRefreshTimer: number | null = null;
  private readonly STORAGE_KEY = 'hk-transit-location-cache';

  private constructor(config?: Partial<LocationCacheConfig>) {
    this.config = {
      maxEntries: 10,
      defaultTTL: 5 * 60 * 1000, // 5 minutes
      accuracyThreshold: 100, // 100 meters
      backgroundRefreshInterval: 2 * 60 * 1000, // 2 minutes
      enablePersistence: true,
      ...config
    };

    this.loadFromStorage();
    this.startBackgroundRefresh();
  }

  public static getInstance(config?: Partial<LocationCacheConfig>): LocationCache {
    if (!LocationCache.instance) {
      LocationCache.instance = new LocationCache(config);
    }
    return LocationCache.instance;
  }

  /**
   * Generate cache key for location
   */
  private generateKey(coordinates: LocationCoordinates): string {
    // Round to 4 decimal places for reasonable grouping
    const lat = Math.round(coordinates.lat * 10000) / 10000;
    const lng = Math.round(coordinates.lng * 10000) / 10000;
    return `${lat},${lng}`;
  }

  /**
   * Check if entry is expired
   */
  private isExpired(entry: CachedLocationEntry): boolean {
    const now = Date.now();
    const age = now - entry.timestamp;
    
    // Dynamic TTL based on accuracy
    let ttl = this.config.defaultTTL;
    if (entry.accuracy <= 10) {
      ttl *= 2; // High accuracy lasts longer
    } else if (entry.accuracy >= 100) {
      ttl *= 0.5; // Low accuracy expires faster
    }
    
    return age > ttl;
  }

  /**
   * Check if entry meets accuracy threshold
   */
  private meetsAccuracyThreshold(entry: CachedLocationEntry): boolean {
    return entry.accuracy <= this.config.accuracyThreshold;
  }

  /**
   * Clean up expired and low-quality entries
   */
  private cleanup(): void {
    const now = Date.now();
    const entriesToRemove: string[] = [];

    for (const [key, entry] of this.cache) {
      if (this.isExpired(entry) || !this.meetsAccuracyThreshold(entry)) {
        entriesToRemove.push(key);
      }
    }

    entriesToRemove.forEach(key => this.cache.delete(key));

    // Enforce max entries limit (remove least recently used)
    if (this.cache.size > this.config.maxEntries) {
      const entries = Array.from(this.cache.entries())
        .sort(([, a], [, b]) => a.lastUsed - b.lastUsed);
      
      const toRemove = entries.slice(0, this.cache.size - this.config.maxEntries);
      toRemove.forEach(([key]) => this.cache.delete(key));
    }

    this.saveToStorage();
  }

  /**
   * Store location in cache
   */
  public set(coordinates: LocationCoordinates, displayName: string, source: string): void {
    const key = this.generateKey(coordinates);
    const now = Date.now();
    
    const existing = this.cache.get(key);
    const entry: CachedLocationEntry = {
      id: key,
      coordinates,
      displayName,
      timestamp: now,
      accuracy: coordinates.accuracy || 0,
      source,
      usageCount: existing ? existing.usageCount + 1 : 1,
      lastUsed: now,
      isBackground: false
    };

    // Only cache if it meets accuracy threshold
    if (this.meetsAccuracyThreshold(entry)) {
      this.cache.set(key, entry);
      this.cleanup();
    }
  }

  /**
   * Get location from cache
   */
  public get(coordinates: LocationCoordinates): CachedLocationEntry | null {
    const key = this.generateKey(coordinates);
    const entry = this.cache.get(key);
    
    if (!entry) return null;
    
    if (this.isExpired(entry) || !this.meetsAccuracyThreshold(entry)) {
      this.cache.delete(key);
      this.saveToStorage();
      return null;
    }

    // Update usage statistics
    entry.usageCount++;
    entry.lastUsed = Date.now();
    this.cache.set(key, entry);
    
    return entry;
  }

  /**
   * Get best cached location (most accurate and recent)
   */
  public getBest(): CachedLocationEntry | null {
    this.cleanup();
    
    if (this.cache.size === 0) return null;
    
    const validEntries = Array.from(this.cache.values())
      .filter(entry => !this.isExpired(entry) && this.meetsAccuracyThreshold(entry));
    
    if (validEntries.length === 0) return null;
    
    // Sort by accuracy (lower is better) then by recency
    validEntries.sort((a, b) => {
      const accuracyDiff = a.accuracy - b.accuracy;
      if (Math.abs(accuracyDiff) > 10) return accuracyDiff;
      return b.timestamp - a.timestamp;
    });
    
    const best = validEntries[0];
    best.usageCount++;
    best.lastUsed = Date.now();
    
    return best;
  }

  /**
   * Check if we have a valid cached location near the given coordinates
   */
  public hasNearby(coordinates: LocationCoordinates, radiusMeters: number = 50): CachedLocationEntry | null {
    this.cleanup();
    
    for (const entry of this.cache.values()) {
      if (this.isExpired(entry) || !this.meetsAccuracyThreshold(entry)) continue;
      
      const distance = this.calculateDistance(coordinates, entry.coordinates);
      if (distance <= radiusMeters) {
        entry.usageCount++;
        entry.lastUsed = Date.now();
        return entry;
      }
    }
    
    return null;
  }

  /**
   * Calculate distance between two coordinates in meters
   */
  private calculateDistance(coord1: LocationCoordinates, coord2: LocationCoordinates): number {
    const R = 6371e3; // Earth's radius in meters
    const φ1 = coord1.lat * Math.PI / 180;
    const φ2 = coord2.lat * Math.PI / 180;
    const Δφ = (coord2.lat - coord1.lat) * Math.PI / 180;
    const Δλ = (coord2.lng - coord1.lng) * Math.PI / 180;

    const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
              Math.cos(φ1) * Math.cos(φ2) *
              Math.sin(Δλ/2) * Math.sin(Δλ/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

    return R * c;
  }

  /**
   * Clear all cached locations
   */
  public clear(): void {
    this.cache.clear();
    this.saveToStorage();
  }

  /**
   * Get cache statistics
   */
  public getStats(): {
    totalEntries: number;
    validEntries: number;
    averageAccuracy: number;
    oldestEntry?: number;
    newestEntry?: number;
  } {
    this.cleanup();
    
    const validEntries = Array.from(this.cache.values())
      .filter(entry => !this.isExpired(entry) && this.meetsAccuracyThreshold(entry));
    
    const stats = {
      totalEntries: this.cache.size,
      validEntries: validEntries.length,
      averageAccuracy: 0,
      oldestEntry: undefined as number | undefined,
      newestEntry: undefined as number | undefined
    };
    
    if (validEntries.length > 0) {
      stats.averageAccuracy = validEntries.reduce((sum, entry) => sum + entry.accuracy, 0) / validEntries.length;
      stats.oldestEntry = Math.min(...validEntries.map(entry => entry.timestamp));
      stats.newestEntry = Math.max(...validEntries.map(entry => entry.timestamp));
    }
    
    return stats;
  }

  /**
   * Load cache from localStorage
   */
  private loadFromStorage(): void {
    if (!this.config.enablePersistence) return;
    
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      if (stored) {
        const data = JSON.parse(stored);
        this.cache = new Map(data);
        this.cleanup(); // Clean up any expired entries
      }
    } catch (error) {
      console.warn('Failed to load location cache from storage:', error);
    }
  }

  /**
   * Save cache to localStorage
   */
  private saveToStorage(): void {
    if (!this.config.enablePersistence) return;
    
    try {
      const data = Array.from(this.cache.entries());
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(data));
    } catch (error) {
      console.warn('Failed to save location cache to storage:', error);
    }
  }

  /**
   * Start background refresh timer
   */
  private startBackgroundRefresh(): void {
    if (this.backgroundRefreshTimer) {
      clearInterval(this.backgroundRefreshTimer);
    }
    
    this.backgroundRefreshTimer = window.setInterval(() => {
      this.cleanup();
    }, this.config.backgroundRefreshInterval);
  }

  /**
   * Stop background refresh timer
   */
  public stopBackgroundRefresh(): void {
    if (this.backgroundRefreshTimer) {
      clearInterval(this.backgroundRefreshTimer);
      this.backgroundRefreshTimer = null;
    }
  }

  /**
   * Update cache configuration
   */
  public updateConfig(newConfig: Partial<LocationCacheConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.cleanup();
    this.startBackgroundRefresh();
  }
}

// Export singleton instance
export const locationCache = LocationCache.getInstance();
