/**
 * Network Connectivity Service for HK Transit Hub
 * Monitors network status and provides connectivity-aware functionality
 */

export interface NetworkStatus {
  isOnline: boolean;
  connectionType: 'wifi' | 'cellular' | 'ethernet' | 'unknown';
  effectiveType: 'slow-2g' | '2g' | '3g' | '4g' | 'unknown';
  downlink?: number; // Mbps
  rtt?: number; // Round trip time in ms
  saveData?: boolean;
}

export interface NetworkChangeEvent {
  status: NetworkStatus;
  previousStatus: NetworkStatus | null;
  timestamp: number;
}

type NetworkChangeListener = (event: NetworkChangeEvent) => void;

class NetworkService {
  private static instance: NetworkService;
  private listeners: Set<NetworkChangeListener> = new Set();
  private currentStatus: NetworkStatus;
  private previousStatus: NetworkStatus | null = null;
  private isMonitoring = false;

  private constructor() {
    this.currentStatus = this.getCurrentNetworkStatus();
    this.startMonitoring();
  }

  public static getInstance(): NetworkService {
    if (!NetworkService.instance) {
      NetworkService.instance = new NetworkService();
    }
    return NetworkService.instance;
  }

  /**
   * Get current network status
   */
  private getCurrentNetworkStatus(): NetworkStatus {
    const isOnline = navigator.onLine;
    
    // Get connection info if available
    const connection = (navigator as any).connection || 
                      (navigator as any).mozConnection || 
                      (navigator as any).webkitConnection;

    let connectionType: NetworkStatus['connectionType'] = 'unknown';
    let effectiveType: NetworkStatus['effectiveType'] = 'unknown';
    let downlink: number | undefined;
    let rtt: number | undefined;
    let saveData: boolean | undefined;

    if (connection) {
      // Map connection types
      switch (connection.type) {
        case 'wifi':
          connectionType = 'wifi';
          break;
        case 'cellular':
          connectionType = 'cellular';
          break;
        case 'ethernet':
          connectionType = 'ethernet';
          break;
        default:
          connectionType = 'unknown';
      }

      // Map effective types
      switch (connection.effectiveType) {
        case 'slow-2g':
          effectiveType = 'slow-2g';
          break;
        case '2g':
          effectiveType = '2g';
          break;
        case '3g':
          effectiveType = '3g';
          break;
        case '4g':
          effectiveType = '4g';
          break;
        default:
          effectiveType = 'unknown';
      }

      downlink = connection.downlink;
      rtt = connection.rtt;
      saveData = connection.saveData;
    }

    return {
      isOnline,
      connectionType,
      effectiveType,
      downlink,
      rtt,
      saveData
    };
  }

  /**
   * Start monitoring network changes
   */
  private startMonitoring(): void {
    if (this.isMonitoring) return;

    this.isMonitoring = true;

    // Listen for online/offline events
    window.addEventListener('online', this.handleNetworkChange);
    window.addEventListener('offline', this.handleNetworkChange);

    // Listen for connection changes if supported
    const connection = (navigator as any).connection || 
                      (navigator as any).mozConnection || 
                      (navigator as any).webkitConnection;

    if (connection) {
      connection.addEventListener('change', this.handleNetworkChange);
    }
  }

  /**
   * Handle network status changes
   */
  private handleNetworkChange = (): void => {
    const newStatus = this.getCurrentNetworkStatus();
    
    // Check if status actually changed
    if (this.hasStatusChanged(this.currentStatus, newStatus)) {
      this.previousStatus = this.currentStatus;
      this.currentStatus = newStatus;

      const event: NetworkChangeEvent = {
        status: newStatus,
        previousStatus: this.previousStatus,
        timestamp: Date.now()
      };

      // Notify all listeners
      this.listeners.forEach(listener => {
        try {
          listener(event);
        } catch (error) {
          console.error('Error in network change listener:', error);
        }
      });
    }
  };

  /**
   * Check if network status has meaningfully changed
   */
  private hasStatusChanged(oldStatus: NetworkStatus, newStatus: NetworkStatus): boolean {
    return (
      oldStatus.isOnline !== newStatus.isOnline ||
      oldStatus.connectionType !== newStatus.connectionType ||
      oldStatus.effectiveType !== newStatus.effectiveType ||
      Math.abs((oldStatus.downlink || 0) - (newStatus.downlink || 0)) > 0.5 ||
      Math.abs((oldStatus.rtt || 0) - (newStatus.rtt || 0)) > 50
    );
  }

  /**
   * Get current network status
   */
  public getStatus(): NetworkStatus {
    return { ...this.currentStatus };
  }

  /**
   * Check if currently online
   */
  public isOnline(): boolean {
    return this.currentStatus.isOnline;
  }

  /**
   * Check if connection is fast enough for certain operations
   */
  public isFastConnection(): boolean {
    const { effectiveType, downlink } = this.currentStatus;
    
    if (effectiveType === '4g') return true;
    if (effectiveType === '3g' && (downlink || 0) > 1) return true;
    
    return false;
  }

  /**
   * Check if user has data saving enabled
   */
  public isDataSavingEnabled(): boolean {
    return this.currentStatus.saveData === true;
  }

  /**
   * Get recommended timeout based on connection quality
   */
  public getRecommendedTimeout(): number {
    const { effectiveType, rtt } = this.currentStatus;
    
    if (!this.isOnline()) return 30000; // 30 seconds for offline
    
    switch (effectiveType) {
      case 'slow-2g':
        return 30000; // 30 seconds
      case '2g':
        return 20000; // 20 seconds
      case '3g':
        return 15000; // 15 seconds
      case '4g':
        return 10000; // 10 seconds
      default:
        // Use RTT if available
        if (rtt) {
          return Math.max(10000, rtt * 20); // At least 10 seconds, or 20x RTT
        }
        return 15000; // Default 15 seconds
    }
  }

  /**
   * Get recommended geolocation options based on network
   */
  public getGeolocationOptions(): {
    enableHighAccuracy: boolean;
    timeout: number;
    maximumAge: number;
  } {
    const timeout = this.getRecommendedTimeout();
    const isFast = this.isFastConnection();
    const isDataSaving = this.isDataSavingEnabled();

    return {
      enableHighAccuracy: isFast && !isDataSaving,
      timeout,
      maximumAge: isDataSaving ? 300000 : 60000 // 5 minutes if data saving, 1 minute otherwise
    };
  }

  /**
   * Add network change listener
   */
  public addListener(listener: NetworkChangeListener): void {
    this.listeners.add(listener);
  }

  /**
   * Remove network change listener
   */
  public removeListener(listener: NetworkChangeListener): void {
    this.listeners.delete(listener);
  }

  /**
   * Test network connectivity with a ping
   */
  public async testConnectivity(url: string = 'https://www.google.com/favicon.ico'): Promise<boolean> {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000);

      const response = await fetch(url, {
        method: 'HEAD',
        mode: 'no-cors',
        signal: controller.signal,
        cache: 'no-cache'
      });

      clearTimeout(timeoutId);
      return true;
    } catch (error) {
      console.warn('Connectivity test failed:', error);
      return false;
    }
  }

  /**
   * Stop monitoring (cleanup)
   */
  public stopMonitoring(): void {
    if (!this.isMonitoring) return;

    this.isMonitoring = false;

    window.removeEventListener('online', this.handleNetworkChange);
    window.removeEventListener('offline', this.handleNetworkChange);

    const connection = (navigator as any).connection || 
                      (navigator as any).mozConnection || 
                      (navigator as any).webkitConnection;

    if (connection) {
      connection.removeEventListener('change', this.handleNetworkChange);
    }

    this.listeners.clear();
  }

  /**
   * Get network quality score (0-1, higher is better)
   */
  public getQualityScore(): number {
    if (!this.isOnline()) return 0;

    const { effectiveType, downlink, rtt } = this.currentStatus;
    let score = 0.5; // Base score for being online

    // Score based on effective type
    switch (effectiveType) {
      case '4g':
        score += 0.4;
        break;
      case '3g':
        score += 0.3;
        break;
      case '2g':
        score += 0.1;
        break;
      case 'slow-2g':
        score += 0.05;
        break;
    }

    // Adjust based on downlink speed
    if (downlink) {
      if (downlink > 10) score += 0.1;
      else if (downlink > 5) score += 0.05;
    }

    // Adjust based on RTT
    if (rtt) {
      if (rtt < 100) score += 0.05;
      else if (rtt > 500) score -= 0.05;
    }

    return Math.max(0, Math.min(1, score));
  }
}

// Export singleton instance
export const networkService = NetworkService.getInstance();
