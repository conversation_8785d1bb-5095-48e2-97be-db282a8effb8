/**
 * Geolocation Debug Utilities
 * Helps troubleshoot geolocation issues in development and production
 */

import { geolocationService } from '../services/geolocationService';
import { locationCache } from '../services/locationCache';
import { reverseGeocodingService } from '../services/reverseGeocodingService';
import { networkService } from '../services/networkService';

export interface GeolocationDebugInfo {
  browser: {
    isSupported: boolean;
    userAgent: string;
    isSecureContext: boolean;
    permissions?: string;
  };
  network: {
    isOnline: boolean;
    connectionType: string;
    effectiveType: string;
    downlink?: number;
    rtt?: number;
    saveData?: boolean;
    qualityScore: number;
  };
  cache: {
    hasCache: boolean;
    stats: any;
    age?: number;
    accuracy?: number;
  };
  geocoding: {
    cacheStats: any;
  };
  location?: {
    coordinates: any;
    accuracy: string;
    source: string;
    displayName: string;
  };
  errors?: string[];
}

class GeolocationDebugger {
  private static instance: GeolocationDebugger;

  private constructor() {}

  public static getInstance(): GeolocationDebugger {
    if (!GeolocationDebugger.instance) {
      GeolocationDebugger.instance = new GeolocationDebugger();
    }
    return GeolocationDebugger.instance;
  }

  /**
   * Get comprehensive debug information
   */
  public async getDebugInfo(): Promise<GeolocationDebugInfo> {
    const errors: string[] = [];

    // Browser information
    const browser = {
      isSupported: geolocationService.isSupported(),
      userAgent: navigator.userAgent,
      isSecureContext: window.isSecureContext,
      permissions: undefined as string | undefined
    };

    // Check permissions if available
    try {
      const permissionStatus = await geolocationService.checkPermission();
      browser.permissions = permissionStatus;
    } catch (error) {
      errors.push(`Permission check failed: ${error}`);
    }

    // Network information
    const networkStatus = networkService.getStatus();
    const network = {
      isOnline: networkStatus.isOnline,
      connectionType: networkStatus.connectionType,
      effectiveType: networkStatus.effectiveType,
      downlink: networkStatus.downlink,
      rtt: networkStatus.rtt,
      saveData: networkStatus.saveData,
      qualityScore: networkService.getQualityScore()
    };

    // Cache information
    const cacheStatus = geolocationService.getCacheStatus();
    const cache = {
      hasCache: cacheStatus.hasCache,
      stats: cacheStatus.stats,
      age: cacheStatus.age,
      accuracy: cacheStatus.accuracy
    };

    // Geocoding cache information
    const geocoding = {
      cacheStats: reverseGeocodingService.getCacheStats()
    };

    const debugInfo: GeolocationDebugInfo = {
      browser,
      network,
      cache,
      geocoding
    };

    if (errors.length > 0) {
      debugInfo.errors = errors;
    }

    return debugInfo;
  }

  /**
   * Test geolocation functionality
   */
  public async testGeolocation(): Promise<{
    success: boolean;
    result?: any;
    error?: any;
    duration: number;
  }> {
    const startTime = Date.now();

    try {
      const result = await geolocationService.getCurrentLocation({
        useCache: false,
        enableReverseGeocoding: true
      });

      return {
        success: true,
        result,
        duration: Date.now() - startTime
      };
    } catch (error) {
      return {
        success: false,
        error,
        duration: Date.now() - startTime
      };
    }
  }

  /**
   * Test network connectivity
   */
  public async testConnectivity(): Promise<{
    success: boolean;
    duration: number;
  }> {
    const startTime = Date.now();
    const success = await networkService.testConnectivity();

    return {
      success,
      duration: Date.now() - startTime
    };
  }

  /**
   * Generate debug report
   */
  public async generateReport(): Promise<string> {
    const debugInfo = await this.getDebugInfo();
    const locationTest = await this.testGeolocation();
    const connectivityTest = await this.testConnectivity();

    const report = `
# Geolocation Debug Report
Generated: ${new Date().toISOString()}

## Browser Support
- Geolocation Supported: ${debugInfo.browser.isSupported}
- Secure Context (HTTPS): ${debugInfo.browser.isSecureContext}
- Permission Status: ${debugInfo.browser.permissions || 'Unknown'}
- User Agent: ${debugInfo.browser.userAgent}

## Network Status
- Online: ${debugInfo.network.isOnline}
- Connection Type: ${debugInfo.network.connectionType}
- Effective Type: ${debugInfo.network.effectiveType}
- Downlink: ${debugInfo.network.downlink || 'Unknown'} Mbps
- RTT: ${debugInfo.network.rtt || 'Unknown'} ms
- Data Saving: ${debugInfo.network.saveData || false}
- Quality Score: ${debugInfo.network.qualityScore.toFixed(2)}

## Cache Status
- Has Cached Location: ${debugInfo.cache.hasCache}
- Cache Age: ${debugInfo.cache.age ? Math.round(debugInfo.cache.age / 1000) + 's' : 'N/A'}
- Cache Accuracy: ${debugInfo.cache.accuracy ? debugInfo.cache.accuracy + 'm' : 'N/A'}
- Total Cached Entries: ${debugInfo.cache.stats?.totalEntries || 0}
- Valid Entries: ${debugInfo.cache.stats?.validEntries || 0}

## Geocoding Cache
- Total Entries: ${debugInfo.geocoding.cacheStats.totalEntries}
- Valid Entries: ${debugInfo.geocoding.cacheStats.validEntries}

## Location Test
- Success: ${locationTest.success}
- Duration: ${locationTest.duration}ms
${locationTest.success ? `
- Coordinates: ${locationTest.result.coordinates.lat.toFixed(6)}, ${locationTest.result.coordinates.lng.toFixed(6)}
- Accuracy: ${locationTest.result.accuracy}
- Source: ${locationTest.result.source}
- Display Name: ${locationTest.result.displayName}
` : `
- Error Type: ${locationTest.error?.type || 'Unknown'}
- Error Code: ${locationTest.error?.code || 'Unknown'}
- Error Message: ${locationTest.error?.message || 'Unknown'}
`}

## Connectivity Test
- Success: ${connectivityTest.success}
- Duration: ${connectivityTest.duration}ms

${debugInfo.errors && debugInfo.errors.length > 0 ? `
## Errors
${debugInfo.errors.map(error => `- ${error}`).join('\n')}
` : ''}

## Recommendations
${this.generateRecommendations(debugInfo, locationTest, connectivityTest)}
`;

    return report.trim();
  }

  /**
   * Generate recommendations based on debug info
   */
  private generateRecommendations(
    debugInfo: GeolocationDebugInfo,
    locationTest: any,
    connectivityTest: any
  ): string {
    const recommendations: string[] = [];

    if (!debugInfo.browser.isSupported) {
      recommendations.push('- Browser does not support geolocation. Use a modern browser.');
    }

    if (!debugInfo.browser.isSecureContext) {
      recommendations.push('- Page is not served over HTTPS. Geolocation requires secure context.');
    }

    if (debugInfo.browser.permissions === 'denied') {
      recommendations.push('- Location permission denied. Enable in browser settings.');
    }

    if (!debugInfo.network.isOnline) {
      recommendations.push('- Device is offline. Check internet connection.');
    }

    if (debugInfo.network.qualityScore < 0.3) {
      recommendations.push('- Poor network quality detected. Consider using cached locations.');
    }

    if (!locationTest.success && locationTest.error?.type === 'timeout') {
      recommendations.push('- Location requests timing out. Try disabling high accuracy mode.');
    }

    if (!connectivityTest.success) {
      recommendations.push('- Network connectivity issues detected. Check firewall/proxy settings.');
    }

    if (debugInfo.cache.stats?.validEntries === 0) {
      recommendations.push('- No cached locations available. First location request may be slower.');
    }

    if (recommendations.length === 0) {
      recommendations.push('- All systems appear to be functioning normally.');
    }

    return recommendations.join('\n');
  }

  /**
   * Log debug information to console
   */
  public async logDebugInfo(): Promise<void> {
    const debugInfo = await this.getDebugInfo();
    
    console.group('🗺️ Geolocation Debug Information');
    console.log('Browser Support:', debugInfo.browser);
    console.log('Network Status:', debugInfo.network);
    console.log('Cache Status:', debugInfo.cache);
    console.log('Geocoding Cache:', debugInfo.geocoding);
    
    if (debugInfo.errors && debugInfo.errors.length > 0) {
      console.error('Errors:', debugInfo.errors);
    }
    
    console.groupEnd();
  }

  /**
   * Clear all caches for testing
   */
  public clearAllCaches(): void {
    geolocationService.clearCache();
    reverseGeocodingService.clearCache();
    console.log('All geolocation caches cleared');
  }
}

// Export singleton instance
export const geolocationDebugger = GeolocationDebugger.getInstance();

// Global debug function for console access
(window as any).debugGeolocation = async () => {
  const report = await geolocationDebugger.generateReport();
  console.log(report);
  return report;
};
