/**
 * Comprehensive tests for enhanced geolocation functionality
 */

import { geolocationService } from '../services/geolocationService';
import { locationCache } from '../services/locationCache';
import { reverseGeocodingService } from '../services/reverseGeocodingService';
import { networkService } from '../services/networkService';

// Mock the global navigator object
const mockNavigator = {
  geolocation: {
    getCurrentPosition: jest.fn(),
    watchPosition: jest.fn(),
    clearWatch: jest.fn()
  },
  onLine: true,
  permissions: {
    query: jest.fn()
  }
};

// Mock fetch for reverse geocoding
global.fetch = jest.fn();

// Setup mocks
beforeEach(() => {
  // Reset all mocks
  jest.clearAllMocks();
  
  // Mock navigator
  Object.defineProperty(global, 'navigator', {
    value: mockNavigator,
    writable: true
  });

  // Clear caches
  geolocationService.clearCache();
  locationCache.clear();
  reverseGeocodingService.clearCache();
});

describe('GeolocationService', () => {
  describe('isSupported', () => {
    it('should return true when geolocation is supported', () => {
      expect(geolocationService.isSupported()).toBe(true);
    });

    it('should return false when geolocation is not supported', () => {
      delete (global.navigator as any).geolocation;
      expect(geolocationService.isSupported()).toBe(false);
    });
  });

  describe('checkPermission', () => {
    it('should return permission status when permissions API is available', async () => {
      mockNavigator.permissions.query.mockResolvedValue({ state: 'granted' });
      
      const status = await geolocationService.checkPermission();
      expect(status).toBe('granted');
      expect(mockNavigator.permissions.query).toHaveBeenCalledWith({ name: 'geolocation' });
    });

    it('should return "unsupported" when geolocation is not supported', async () => {
      delete (global.navigator as any).geolocation;
      
      const status = await geolocationService.checkPermission();
      expect(status).toBe('unsupported');
    });

    it('should return "prompt" when permissions API is not available', async () => {
      delete (global.navigator as any).permissions;
      
      const status = await geolocationService.checkPermission();
      expect(status).toBe('prompt');
    });
  });

  describe('getCurrentLocation', () => {
    const mockPosition = {
      coords: {
        latitude: 22.3193,
        longitude: 114.1694,
        accuracy: 10,
        altitude: null,
        altitudeAccuracy: null,
        heading: null,
        speed: null
      },
      timestamp: Date.now()
    };

    it('should successfully get location with high accuracy', async () => {
      mockNavigator.geolocation.getCurrentPosition.mockImplementation((success) => {
        success(mockPosition);
      });

      const result = await geolocationService.getCurrentLocation({
        enableHighAccuracy: true
      });

      expect(result.coordinates.lat).toBe(22.3193);
      expect(result.coordinates.lng).toBe(114.1694);
      expect(result.accuracy).toBe('high');
      expect(result.source).toBe('gps');
    });

    it('should fallback to lower accuracy on high accuracy failure', async () => {
      let callCount = 0;
      mockNavigator.geolocation.getCurrentPosition.mockImplementation((success, error) => {
        callCount++;
        if (callCount === 1) {
          // First call (high accuracy) fails
          error({ code: 3, message: 'Timeout' });
        } else {
          // Second call (lower accuracy) succeeds
          success({ ...mockPosition, coords: { ...mockPosition.coords, accuracy: 50 } });
        }
      });

      const result = await geolocationService.getCurrentLocation();

      expect(result.coordinates.lat).toBe(22.3193);
      expect(result.accuracy).toBe('medium');
      expect(mockNavigator.geolocation.getCurrentPosition).toHaveBeenCalledTimes(2);
    });

    it('should handle permission denied error', async () => {
      mockNavigator.geolocation.getCurrentPosition.mockImplementation((success, error) => {
        error({ code: 1, message: 'Permission denied' });
      });

      await expect(geolocationService.getCurrentLocation()).rejects.toMatchObject({
        code: 1,
        type: 'permission_denied'
      });
    });

    it('should return cached location when available', async () => {
      // First call to populate cache
      mockNavigator.geolocation.getCurrentPosition.mockImplementation((success) => {
        success(mockPosition);
      });

      await geolocationService.getCurrentLocation();

      // Second call should use cache
      const result = await geolocationService.getCurrentLocation({ useCache: true });
      
      expect(result.source).toBe('cache');
      expect(mockNavigator.geolocation.getCurrentPosition).toHaveBeenCalledTimes(1);
    });

    it('should validate coordinates are within reasonable bounds', async () => {
      const invalidPosition = {
        ...mockPosition,
        coords: { ...mockPosition.coords, latitude: 0, longitude: 0 }
      };

      mockNavigator.geolocation.getCurrentPosition.mockImplementation((success, error) => {
        success(invalidPosition);
      });

      await expect(geolocationService.getCurrentLocation()).rejects.toMatchObject({
        type: 'position_unavailable'
      });
    });
  });

  describe('Network-aware geolocation', () => {
    it('should adjust timeout based on network conditions', async () => {
      // Mock slow network
      jest.spyOn(networkService, 'getRecommendedTimeout').mockReturnValue(20000);
      jest.spyOn(networkService, 'isFastConnection').mockReturnValue(false);

      mockNavigator.geolocation.getCurrentPosition.mockImplementation((success) => {
        success(mockPosition);
      });

      await geolocationService.getCurrentLocation();

      // Verify that longer timeout was used for slow network
      const lastCall = mockNavigator.geolocation.getCurrentPosition.mock.calls.slice(-1)[0];
      expect(lastCall[2].timeout).toBeGreaterThan(15000);
    });

    it('should disable high accuracy on slow connections', async () => {
      jest.spyOn(networkService, 'isFastConnection').mockReturnValue(false);
      jest.spyOn(networkService, 'isDataSavingEnabled').mockReturnValue(true);

      mockNavigator.geolocation.getCurrentPosition.mockImplementation((success) => {
        success(mockPosition);
      });

      await geolocationService.getCurrentLocation();

      const lastCall = mockNavigator.geolocation.getCurrentPosition.mock.calls.slice(-1)[0];
      expect(lastCall[2].enableHighAccuracy).toBe(false);
    });
  });
});

describe('LocationCache', () => {
  const testCoordinates = { lat: 22.3193, lng: 114.1694, accuracy: 10 };

  it('should store and retrieve cached locations', () => {
    locationCache.set(testCoordinates, 'Test Location', 'gps');
    
    const cached = locationCache.get(testCoordinates);
    expect(cached).toBeTruthy();
    expect(cached?.displayName).toBe('Test Location');
  });

  it('should find nearby cached locations', () => {
    locationCache.set(testCoordinates, 'Test Location', 'gps');
    
    const nearbyCoords = { lat: 22.3194, lng: 114.1695, accuracy: 15 };
    const nearby = locationCache.hasNearby(nearbyCoords, 100);
    
    expect(nearby).toBeTruthy();
    expect(nearby?.displayName).toBe('Test Location');
  });

  it('should expire old cache entries', () => {
    // Mock old timestamp
    const oldEntry = {
      id: 'test',
      coordinates: testCoordinates,
      displayName: 'Old Location',
      timestamp: Date.now() - 10 * 60 * 1000, // 10 minutes ago
      accuracy: 10,
      source: 'gps',
      usageCount: 1,
      lastUsed: Date.now() - 10 * 60 * 1000
    };

    locationCache['cache'].set('test', oldEntry);
    
    const cached = locationCache.get(testCoordinates);
    expect(cached).toBeNull();
  });

  it('should return best cached location based on accuracy', () => {
    const coords1 = { lat: 22.3193, lng: 114.1694, accuracy: 50 };
    const coords2 = { lat: 22.3194, lng: 114.1695, accuracy: 10 };

    locationCache.set(coords1, 'Location 1', 'network');
    locationCache.set(coords2, 'Location 2', 'gps');

    const best = locationCache.getBest();
    expect(best?.displayName).toBe('Location 2');
    expect(best?.accuracy).toBe(10);
  });
});

describe('ReverseGeocodingService', () => {
  const testCoordinates = { lat: 22.3193, lng: 114.1694 };

  beforeEach(() => {
    (global.fetch as jest.Mock).mockClear();
  });

  it('should successfully reverse geocode coordinates', async () => {
    const mockResponse = {
      display_name: 'Central, Hong Kong',
      address: {
        road: 'Des Voeux Road Central',
        suburb: 'Central',
        city_district: 'Central and Western',
        city: 'Hong Kong',
        country: 'Hong Kong'
      }
    };

    (global.fetch as jest.Mock).mockResolvedValue({
      ok: true,
      json: () => Promise.resolve(mockResponse)
    });

    const result = await reverseGeocodingService.reverseGeocode(testCoordinates);

    expect(result.displayName).toContain('Central');
    expect(result.source).toBe('nominatim');
    expect(result.confidence).toBeGreaterThan(0);
  });

  it('should return fallback name on geocoding failure', async () => {
    (global.fetch as jest.Mock).mockRejectedValue(new Error('Network error'));

    const result = await reverseGeocodingService.reverseGeocode(testCoordinates);

    expect(result.displayName).toContain('Location');
    expect(result.source).toBe('fallback');
    expect(result.confidence).toBe(0.1);
  });

  it('should use cached results when available', async () => {
    const mockResponse = {
      display_name: 'Cached Location',
      address: { city: 'Hong Kong' }
    };

    (global.fetch as jest.Mock).mockResolvedValue({
      ok: true,
      json: () => Promise.resolve(mockResponse)
    });

    // First call
    await reverseGeocodingService.reverseGeocode(testCoordinates);
    
    // Second call should use cache
    const result = await reverseGeocodingService.reverseGeocode(testCoordinates);

    expect(result.source).toBe('cache');
    expect(global.fetch).toHaveBeenCalledTimes(1);
  });

  it('should respect rate limiting', async () => {
    const mockResponse = { display_name: 'Test', address: {} };
    (global.fetch as jest.Mock).mockResolvedValue({
      ok: true,
      json: () => Promise.resolve(mockResponse)
    });

    const startTime = Date.now();
    
    // Make two rapid calls
    await reverseGeocodingService.reverseGeocode(testCoordinates, { useCache: false });
    await reverseGeocodingService.reverseGeocode(
      { lat: 22.3194, lng: 114.1695 }, 
      { useCache: false }
    );

    const endTime = Date.now();
    
    // Should take at least 1 second due to rate limiting
    expect(endTime - startTime).toBeGreaterThan(900);
  });
});

describe('NetworkService', () => {
  it('should detect online status', () => {
    expect(networkService.isOnline()).toBe(true);
    
    Object.defineProperty(navigator, 'onLine', { value: false, writable: true });
    expect(networkService.getStatus().isOnline).toBe(false);
  });

  it('should provide network-appropriate geolocation options', () => {
    jest.spyOn(networkService, 'isFastConnection').mockReturnValue(true);
    jest.spyOn(networkService, 'isDataSavingEnabled').mockReturnValue(false);

    const options = networkService.getGeolocationOptions();
    
    expect(options.enableHighAccuracy).toBe(true);
    expect(options.timeout).toBeLessThan(15000);
  });

  it('should adjust options for slow connections', () => {
    jest.spyOn(networkService, 'isFastConnection').mockReturnValue(false);
    jest.spyOn(networkService, 'isDataSavingEnabled').mockReturnValue(true);

    const options = networkService.getGeolocationOptions();
    
    expect(options.enableHighAccuracy).toBe(false);
    expect(options.maximumAge).toBeGreaterThan(60000);
  });
});

describe('Integration Tests', () => {
  it('should handle complete geolocation workflow with reverse geocoding', async () => {
    const mockPosition = {
      coords: { latitude: 22.3193, longitude: 114.1694, accuracy: 10 },
      timestamp: Date.now()
    };

    const mockGeocodingResponse = {
      display_name: 'Central, Hong Kong',
      address: { city: 'Hong Kong' }
    };

    mockNavigator.geolocation.getCurrentPosition.mockImplementation((success) => {
      success(mockPosition);
    });

    (global.fetch as jest.Mock).mockResolvedValue({
      ok: true,
      json: () => Promise.resolve(mockGeocodingResponse)
    });

    const result = await geolocationService.getCurrentLocation({
      enableReverseGeocoding: true,
      useCache: true
    });

    expect(result.coordinates.lat).toBe(22.3193);
    expect(result.displayName).toContain('Central');
    expect(result.accuracy).toBe('high');
  });
});
