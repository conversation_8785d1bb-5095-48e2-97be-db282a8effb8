# Enhanced Geolocation System

The HK Transit Hub features a comprehensive geolocation system designed for accuracy, reliability, and optimal user experience in Hong Kong's urban environment.

## Features

### 🎯 Progressive Accuracy Strategy
- **Multi-tier fallback**: Starts with high accuracy GPS and progressively falls back to network-based positioning
- **Network-aware optimization**: Adjusts accuracy settings based on connection quality
- **Smart timeout management**: Dynamic timeouts based on network conditions

### 🗺️ Intelligent Caching
- **Multi-level caching**: Both in-memory and persistent storage
- **Accuracy-based expiration**: High-accuracy locations cached longer
- **Proximity detection**: Finds nearby cached locations to avoid redundant requests
- **Background cleanup**: Automatic removal of expired and low-quality entries

### 🌐 Reverse Geocoding
- **Multiple providers**: Primary Nominatim (OpenStreetMap) with fallback options
- **Hong Kong optimized**: Special handling for HK addresses and districts
- **Rate limiting**: Respects API limits with intelligent request spacing
- **Cached results**: 24-hour caching to minimize API calls

### 📡 Network Awareness
- **Connection quality detection**: Monitors 2G/3G/4G/WiFi status
- **Data saving mode**: Respects user's data saving preferences
- **Offline handling**: Graceful degradation when network is unavailable
- **Adaptive timeouts**: Longer timeouts for slower connections

### 🔐 Enhanced Permission Handling
- **Permission status checking**: Uses Permissions API when available
- **User guidance**: Provides specific instructions for permission issues
- **Graceful fallbacks**: Handles denied permissions elegantly

## Architecture

```
GeolocationService (Main Interface)
├── LocationCache (Intelligent Caching)
├── ReverseGeocodingService (Address Resolution)
├── NetworkService (Connection Monitoring)
└── Progressive Fallback Strategies
```

## Usage Examples

### Basic Location Retrieval

```typescript
import { geolocationService } from '../services/geolocationService';

// Simple location request
const location = await geolocationService.getCurrentLocation();
console.log(location.coordinates); // { lat: 22.3193, lng: 114.1694 }
console.log(location.displayName); // "Central, Hong Kong"
```

### Advanced Configuration

```typescript
// High-accuracy location with reverse geocoding
const location = await geolocationService.getCurrentLocation({
  enableHighAccuracy: true,
  enableReverseGeocoding: true,
  useCache: true,
  geocodingOptions: {
    language: 'en',
    includeDistrict: true
  }
});
```

### Permission Handling

```typescript
// Check and request permissions
const permissionResult = await geolocationService.requestPermission();
if (permissionResult.granted) {
  const location = await geolocationService.getCurrentLocation();
} else {
  console.log(permissionResult.guidance); // User-friendly error message
}
```

### Cache Management

```typescript
import { locationCache } from '../services/locationCache';

// Get cache statistics
const stats = locationCache.getStats();
console.log(`${stats.validEntries} cached locations`);

// Find nearby cached location
const nearby = locationCache.hasNearby(coordinates, 100); // 100m radius
```

## Configuration Options

### GeolocationOptions

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| `enableHighAccuracy` | boolean | auto | Use GPS for high accuracy |
| `timeout` | number | auto | Request timeout in ms |
| `maximumAge` | number | auto | Cache age in ms |
| `useCache` | boolean | true | Use cached locations |
| `enableReverseGeocoding` | boolean | false | Convert coordinates to addresses |
| `geocodingOptions` | object | - | Reverse geocoding settings |

### Network-Aware Defaults

The system automatically adjusts settings based on network conditions:

| Connection | High Accuracy | Timeout | Cache Age |
|------------|---------------|---------|-----------|
| 4G/WiFi | ✅ | 10s | 1min |
| 3G | ❌ | 15s | 2min |
| 2G | ❌ | 20s | 5min |
| Offline | ❌ | 30s | 10min |

## Error Handling

### Error Types

- `permission_denied`: User denied location access
- `position_unavailable`: Location cannot be determined
- `timeout`: Request timed out
- `not_supported`: Browser doesn't support geolocation
- `network_error`: Network connectivity issues

### Error Recovery

```typescript
try {
  const location = await geolocationService.getCurrentLocation();
} catch (error) {
  switch (error.type) {
    case 'permission_denied':
      // Show permission guidance
      showPermissionDialog(error.message);
      break;
    case 'timeout':
      // Retry with longer timeout
      retryWithFallback();
      break;
    default:
      // Show generic error
      showError(error.message);
  }
}
```

## Performance Optimizations

### Caching Strategy
- **Accuracy-based TTL**: High accuracy (≤10m) cached for 10 minutes
- **Usage tracking**: Frequently used locations prioritized
- **Proximity grouping**: Similar coordinates share cache entries
- **Background cleanup**: Automatic removal of stale data

### Network Optimization
- **Request batching**: Multiple geocoding requests combined
- **Rate limiting**: Respects API limits (1 request/second)
- **Compression**: Minimal data transfer for mobile users
- **Offline support**: Cached data available without network

### Memory Management
- **LRU eviction**: Least recently used entries removed first
- **Size limits**: Maximum 10 cached locations by default
- **Weak references**: Prevents memory leaks in long-running apps

## Hong Kong Specific Features

### Location Validation
- **Boundary checking**: Validates coordinates are within Hong Kong
- **District recognition**: Identifies HK districts and regions
- **Transport hubs**: Special handling for MTR stations and bus terminals

### Address Formatting
- **Bilingual support**: English and Traditional Chinese
- **Local conventions**: HK-style address formatting
- **Landmark integration**: Recognition of major HK landmarks

## Testing

### Unit Tests
```bash
npm test -- geolocation.test.ts
```

### Integration Tests
- Mock geolocation API responses
- Test network condition scenarios
- Validate caching behavior
- Check error handling paths

### Manual Testing Checklist
- [ ] Location permission flow
- [ ] High accuracy vs network positioning
- [ ] Cache hit/miss scenarios
- [ ] Network connectivity changes
- [ ] Error state handling
- [ ] Reverse geocoding accuracy

## Troubleshooting

### Common Issues

**Location not found**
- Check browser permissions
- Verify network connectivity
- Try disabling high accuracy mode

**Slow location detection**
- Check network speed
- Clear location cache
- Reduce accuracy requirements

**Inaccurate addresses**
- Verify coordinates are in Hong Kong
- Check reverse geocoding service status
- Clear geocoding cache

### Debug Information

Enable debug logging:
```typescript
// Check cache status
const cacheStatus = geolocationService.getCacheStatus();
console.log('Cache:', cacheStatus);

// Check network conditions
const networkStatus = networkService.getStatus();
console.log('Network:', networkStatus);
```

## Browser Compatibility

| Browser | Geolocation | Permissions API | Network Info |
|---------|-------------|-----------------|--------------|
| Chrome 90+ | ✅ | ✅ | ✅ |
| Firefox 88+ | ✅ | ✅ | ❌ |
| Safari 14+ | ✅ | ❌ | ❌ |
| Edge 90+ | ✅ | ✅ | ✅ |

## Security Considerations

- **HTTPS required**: Geolocation only works over secure connections
- **Permission persistence**: Browser remembers user choices
- **Data privacy**: No location data sent to external servers
- **Local storage**: Cached data stays on device

## Future Enhancements

- [ ] Indoor positioning support
- [ ] Bluetooth beacon integration
- [ ] Machine learning accuracy improvements
- [ ] Real-time location tracking
- [ ] Geofencing capabilities
