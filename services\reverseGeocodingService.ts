/**
 * Reverse Geocoding Service for HK Transit Hub
 * Converts coordinates to meaningful location names using multiple providers
 */

import { LocationCoordinates } from './geolocationService';

export interface ReverseGeocodingResult {
  displayName: string;
  address: string;
  district?: string;
  region?: string;
  country?: string;
  confidence: number;
  source: 'nominatim' | 'fallback' | 'cache';
}

export interface ReverseGeocodingOptions {
  language?: 'en' | 'zh';
  includeDistrict?: boolean;
  timeout?: number;
  useCache?: boolean;
}

interface CachedGeocodingResult {
  result: ReverseGeocodingResult;
  timestamp: number;
  coordinates: LocationCoordinates;
}

class ReverseGeocodingService {
  private static instance: ReverseGeocodingService;
  private cache = new Map<string, CachedGeocodingResult>();
  private readonly CACHE_DURATION = 24 * 60 * 60 * 1000; // 24 hours
  private readonly NOMINATIM_BASE_URL = 'https://nominatim.openstreetmap.org/reverse';
  private readonly REQUEST_DELAY = 1000; // 1 second between requests to respect rate limits
  private lastRequestTime = 0;

  private constructor() {}

  public static getInstance(): ReverseGeocodingService {
    if (!ReverseGeocodingService.instance) {
      ReverseGeocodingService.instance = new ReverseGeocodingService();
    }
    return ReverseGeocodingService.instance;
  }

  /**
   * Generate cache key for coordinates
   */
  private generateCacheKey(coordinates: LocationCoordinates): string {
    // Round to 4 decimal places for reasonable caching
    const lat = Math.round(coordinates.lat * 10000) / 10000;
    const lng = Math.round(coordinates.lng * 10000) / 10000;
    return `${lat},${lng}`;
  }

  /**
   * Check if cached result is valid
   */
  private isCacheValid(cached: CachedGeocodingResult): boolean {
    const now = Date.now();
    return (now - cached.timestamp) < this.CACHE_DURATION;
  }

  /**
   * Get cached result if available
   */
  private getCachedResult(coordinates: LocationCoordinates): ReverseGeocodingResult | null {
    const key = this.generateCacheKey(coordinates);
    const cached = this.cache.get(key);
    
    if (cached && this.isCacheValid(cached)) {
      return { ...cached.result, source: 'cache' };
    }
    
    if (cached) {
      this.cache.delete(key);
    }
    
    return null;
  }

  /**
   * Cache geocoding result
   */
  private cacheResult(coordinates: LocationCoordinates, result: ReverseGeocodingResult): void {
    const key = this.generateCacheKey(coordinates);
    this.cache.set(key, {
      result,
      timestamp: Date.now(),
      coordinates
    });
  }

  /**
   * Rate limit requests to respect API limits
   */
  private async respectRateLimit(): Promise<void> {
    const now = Date.now();
    const timeSinceLastRequest = now - this.lastRequestTime;
    
    if (timeSinceLastRequest < this.REQUEST_DELAY) {
      const waitTime = this.REQUEST_DELAY - timeSinceLastRequest;
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }
    
    this.lastRequestTime = Date.now();
  }

  /**
   * Reverse geocode using Nominatim (OpenStreetMap)
   */
  private async reverseGeocodeNominatim(
    coordinates: LocationCoordinates, 
    options: ReverseGeocodingOptions
  ): Promise<ReverseGeocodingResult> {
    await this.respectRateLimit();

    const params = new URLSearchParams({
      lat: coordinates.lat.toString(),
      lon: coordinates.lng.toString(),
      format: 'json',
      'accept-language': options.language === 'zh' ? 'zh-HK,zh,en' : 'en',
      zoom: '18',
      addressdetails: '1'
    });

    const url = `${this.NOMINATIM_BASE_URL}?${params}`;
    
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), options.timeout || 10000);

    try {
      const response = await fetch(url, {
        signal: controller.signal,
        headers: {
          'User-Agent': 'HK-Transit-Hub/1.0'
        }
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      
      if (!data || data.error) {
        throw new Error(data?.error || 'No results found');
      }

      return this.parseNominatimResult(data, options);

    } catch (error) {
      clearTimeout(timeoutId);
      console.warn('Nominatim geocoding failed:', error);
      throw error;
    }
  }

  /**
   * Parse Nominatim API response
   */
  private parseNominatimResult(data: any, options: ReverseGeocodingOptions): ReverseGeocodingResult {
    const address = data.address || {};
    
    // Build display name prioritizing Hong Kong specific components
    let displayName = '';
    
    if (address.building || address.house_number) {
      const building = address.building || '';
      const houseNumber = address.house_number || '';
      displayName += `${building} ${houseNumber}`.trim() + ', ';
    }
    
    if (address.road) {
      displayName += address.road + ', ';
    }
    
    if (address.suburb || address.neighbourhood) {
      displayName += (address.suburb || address.neighbourhood) + ', ';
    }
    
    if (address.city_district || address.district) {
      displayName += (address.city_district || address.district) + ', ';
    }
    
    if (address.city || address.town) {
      displayName += (address.city || address.town);
    } else {
      displayName += 'Hong Kong';
    }
    
    // Clean up display name
    displayName = displayName.replace(/,\s*$/, '').replace(/,\s*,/g, ',');
    
    if (!displayName || displayName.trim() === '') {
      displayName = data.display_name || 'Unknown Location';
    }

    return {
      displayName: displayName.trim(),
      address: data.display_name || displayName,
      district: address.city_district || address.district || address.suburb,
      region: address.state || address.region || 'Hong Kong',
      country: address.country || 'Hong Kong',
      confidence: this.calculateConfidence(data),
      source: 'nominatim'
    };
  }

  /**
   * Calculate confidence score based on result quality
   */
  private calculateConfidence(data: any): number {
    let confidence = 0.5; // Base confidence
    
    const address = data.address || {};
    
    // Increase confidence based on available address components
    if (address.house_number) confidence += 0.2;
    if (address.road) confidence += 0.15;
    if (address.suburb || address.neighbourhood) confidence += 0.1;
    if (address.city_district || address.district) confidence += 0.05;
    
    // Bonus for Hong Kong locations
    if (address.country === 'Hong Kong' || address.country === '香港') {
      confidence += 0.1;
    }
    
    return Math.min(confidence, 1.0);
  }

  /**
   * Generate fallback display name
   */
  private generateFallbackName(coordinates: LocationCoordinates): ReverseGeocodingResult {
    const { lat, lng, accuracy } = coordinates;
    const accuracyText = accuracy ? ` (±${Math.round(accuracy)}m)` : '';
    
    return {
      displayName: `Location (${lat.toFixed(4)}, ${lng.toFixed(4)})${accuracyText}`,
      address: `${lat.toFixed(6)}, ${lng.toFixed(6)}`,
      district: undefined,
      region: 'Hong Kong',
      country: 'Hong Kong',
      confidence: 0.1,
      source: 'fallback'
    };
  }

  /**
   * Main reverse geocoding method
   */
  public async reverseGeocode(
    coordinates: LocationCoordinates, 
    options: ReverseGeocodingOptions = {}
  ): Promise<ReverseGeocodingResult> {
    // Check cache first
    if (options.useCache !== false) {
      const cached = this.getCachedResult(coordinates);
      if (cached) {
        return cached;
      }
    }

    try {
      // Try Nominatim first
      const result = await this.reverseGeocodeNominatim(coordinates, options);
      
      // Cache successful result
      this.cacheResult(coordinates, result);
      
      return result;
      
    } catch (error) {
      console.warn('Reverse geocoding failed, using fallback:', error);
      
      // Return fallback result
      const fallback = this.generateFallbackName(coordinates);
      
      // Cache fallback with shorter duration
      this.cacheResult(coordinates, fallback);
      
      return fallback;
    }
  }

  /**
   * Batch reverse geocode multiple coordinates
   */
  public async reverseGeocodeBatch(
    coordinatesList: LocationCoordinates[], 
    options: ReverseGeocodingOptions = {}
  ): Promise<ReverseGeocodingResult[]> {
    const results: ReverseGeocodingResult[] = [];
    
    for (const coordinates of coordinatesList) {
      try {
        const result = await this.reverseGeocode(coordinates, options);
        results.push(result);
      } catch (error) {
        console.warn('Batch geocoding failed for coordinates:', coordinates, error);
        results.push(this.generateFallbackName(coordinates));
      }
    }
    
    return results;
  }

  /**
   * Clear geocoding cache
   */
  public clearCache(): void {
    this.cache.clear();
  }

  /**
   * Get cache statistics
   */
  public getCacheStats(): {
    totalEntries: number;
    validEntries: number;
    oldestEntry?: number;
    newestEntry?: number;
  } {
    const now = Date.now();
    const validEntries = Array.from(this.cache.values())
      .filter(entry => this.isCacheValid(entry));
    
    const stats = {
      totalEntries: this.cache.size,
      validEntries: validEntries.length,
      oldestEntry: undefined as number | undefined,
      newestEntry: undefined as number | undefined
    };
    
    if (validEntries.length > 0) {
      stats.oldestEntry = Math.min(...validEntries.map(entry => entry.timestamp));
      stats.newestEntry = Math.max(...validEntries.map(entry => entry.timestamp));
    }
    
    return stats;
  }
}

// Export singleton instance
export const reverseGeocodingService = ReverseGeocodingService.getInstance();
